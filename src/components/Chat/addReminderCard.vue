<template>
  <div class="add-reminder-card" @click="handleClick">
    <div class="add-reminder-icon">
      <svg width="36" height="36" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M12 5V19M5 12H19"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
    <div class="add-reminder-text">添加提醒</div>
  </div>
</template>

<script setup lang="ts">
// 定义emits
const emit = defineEmits<{
  click: [];
}>();

// 处理点击事件
const handleClick = () => {
  emit('click');
};
</script>

<style lang="scss" scoped>
.add-reminder-card {
  width: 220px;
  height: 220px;
  background: rgba(1, 28, 32, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border-left: 3px solid #00ffff;
  box-shadow: -3px 0 6px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;

  &:hover {
    background: rgba(1, 28, 32, 0.8);
    transform: translateY(-2px);
    box-shadow:
      -3px 0 6px rgba(0, 255, 255, 0.5),
      0 6px 20px rgba(0, 0, 0, 0.3);
  }
}

.add-reminder-icon {
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 12px;
  transition: all 0.3s ease;

  .add-reminder-card:hover & {
    color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
  }
}

.add-reminder-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;

  .add-reminder-card:hover & {
    color: rgba(255, 255, 255, 0.9);
  }
}
</style>
